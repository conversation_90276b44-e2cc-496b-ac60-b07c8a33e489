"""
量化特征工程模块测试脚本
验证所有特征计算函数的正确性和性能
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from quantitative_features import *

def generate_sample_data(n_days=500):
    """生成模拟股票数据用于测试"""
    np.random.seed(42)
    
    # 生成价格数据（几何布朗运动）
    returns = np.random.normal(0.0005, 0.02, n_days)  # 日收益率
    prices = [100]  # 初始价格
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    # 生成成交量数据
    base_volume = 1000000
    volume_noise = np.random.normal(1, 0.3, n_days + 1)
    volumes = base_volume * np.abs(volume_noise)
    
    # 创建DataFrame
    dates = pd.date_range('2023-01-01', periods=n_days + 1, freq='D')
    df = pd.DataFrame({
        'date': dates,
        'close': prices,
        'volume': volumes
    })
    df.set_index('date', inplace=True)
    
    return df

def test_all_features():
    """测试所有特征计算函数"""
    print("=== 量化特征工程模块测试 ===\n")
    
    # 生成测试数据
    print("1. 生成测试数据...")
    stock_data = generate_sample_data(500)
    market_data = generate_sample_data(500)  # 模拟市场指数
    print(f"   股票数据: {len(stock_data)} 天")
    print(f"   数据范围: {stock_data.index[0]} 到 {stock_data.index[-1]}")
    
    # 基础数据
    close = stock_data['close']
    volume = stock_data['volume']
    returns = close.pct_change()
    market_returns = market_data['close'].pct_change()
    
    print("\n2. 测试各类特征计算...")
    
    # 测试排名特征
    print("   测试排名特征...")
    price_rank = calculate_price_rank(close)
    volume_rank = calculate_volume_rank(volume)
    print(f"   价格排名范围: {price_rank.min():.3f} - {price_rank.max():.3f}")
    print(f"   成交量排名范围: {volume_rank.min():.3f} - {volume_rank.max():.3f}")
    
    # 测试风险指标
    print("   测试风险调整指标...")
    skewness = calculate_return_skewness(returns)
    kurtosis = calculate_return_kurtosis(returns)
    sharpe = calculate_sharpe_ratio(returns)
    drawdown = calculate_max_drawdown_rolling(returns)
    print(f"   偏度范围: {skewness.min():.3f} - {skewness.max():.3f}")
    print(f"   峰度范围: {kurtosis.min():.3f} - {kurtosis.max():.3f}")
    print(f"   夏普比率范围: {sharpe.min():.3f} - {sharpe.max():.3f}")
    print(f"   最大回撤范围: {drawdown.min():.3f} - {drawdown.max():.3f}")
    
    # 测试市场相关性指标
    print("   测试市场相关性指标...")
    beta = calculate_beta(returns, market_returns)
    info_ratio = calculate_information_ratio(returns, market_returns)
    print(f"   Beta范围: {beta.min():.3f} - {beta.max():.3f}")
    print(f"   信息比率范围: {info_ratio.min():.3f} - {info_ratio.max():.3f}")
    
    # 测试流动性指标
    print("   测试流动性指标...")
    turnover = calculate_turnover_rate(volume, shares_outstanding=1e8)
    liquidity = calculate_liquidity_score(volume, close)
    print(f"   换手率范围: {turnover.min():.4f} - {turnover.max():.4f}")
    print(f"   流动性评分范围: {liquidity.min():.3f} - {liquidity.max():.3f}")
    
    # 测试动量指标
    print("   测试动量指标...")
    momentum_df = calculate_price_momentum_multiple_periods(close)
    print(f"   动量特征数量: {len(momentum_df.columns)}")
    print(f"   动量特征: {list(momentum_df.columns)}")
    
    # 测试市场微观结构指标
    print("   测试市场微观结构指标...")
    vol_regime = calculate_volatility_regime(returns)
    mean_revert = calculate_mean_reversion_signal(close)
    trend_strength = calculate_trend_strength(close)
    print(f"   波动率状态范围: {vol_regime.min():.3f} - {vol_regime.max():.3f}")
    print(f"   均值回归信号范围: {mean_revert.min():.3f} - {mean_revert.max():.3f}")
    print(f"   趋势强度范围: {trend_strength.min():.3f} - {trend_strength.max():.3f}")
    
    # 测试支撑阻力位
    print("   测试支撑阻力位...")
    support_dist, resistance_dist = calculate_support_resistance_levels(close)
    print(f"   支撑位距离范围: {support_dist.min():.3f} - {support_dist.max():.3f}")
    print(f"   阻力位距离范围: {resistance_dist.min():.3f} - {resistance_dist.max():.3f}")
    
    # 测试市值因子
    print("   测试市值因子...")
    market_cap = calculate_market_cap_factor(close, shares_outstanding=1e8)
    print(f"   对数市值范围: {market_cap.min():.3f} - {market_cap.max():.3f}")
    
    print("\n3. 测试综合特征集...")
    features = create_comprehensive_feature_set(stock_data, market_data, shares_outstanding=1e8)
    print(f"   总特征数量: {len(features.columns)}")
    print(f"   特征完整性: {features.notna().sum().sum()} / {features.size}")
    print(f"   特征列表: {list(features.columns)}")
    
    print("\n4. 数据质量检查...")
    # 检查是否有无穷大值
    inf_count = np.isinf(features.select_dtypes(include=[np.number])).sum().sum()
    print(f"   无穷大值数量: {inf_count}")
    
    # 检查缺失值比例
    missing_ratio = features.isnull().sum() / len(features)
    print(f"   缺失值比例最高的特征: {missing_ratio.max():.3f}")
    
    print("\n=== 测试完成 ===")
    return features

def plot_feature_examples(features):
    """绘制部分特征的示例图表"""
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('量化特征示例图表', fontsize=16)
    
    # 价格排名
    axes[0, 0].plot(features.index, features['price_rank_252d'])
    axes[0, 0].set_title('价格排名 (252日)')
    axes[0, 0].set_ylabel('排名')
    axes[0, 0].grid(True)
    
    # 夏普比率
    axes[0, 1].plot(features.index, features['sharpe_ratio'])
    axes[0, 1].set_title('夏普比率')
    axes[0, 1].set_ylabel('夏普比率')
    axes[0, 1].grid(True)
    
    # 波动率状态
    axes[0, 2].plot(features.index, features['volatility_regime'])
    axes[0, 2].set_title('波动率状态')
    axes[0, 2].set_ylabel('比率')
    axes[0, 2].grid(True)
    
    # 均值回归信号
    axes[1, 0].plot(features.index, features['mean_reversion_signal'])
    axes[1, 0].set_title('均值回归信号')
    axes[1, 0].set_ylabel('信号强度')
    axes[1, 0].grid(True)
    
    # 趋势强度
    axes[1, 1].plot(features.index, features['trend_strength'])
    axes[1, 1].set_title('趋势强度')
    axes[1, 1].set_ylabel('强度')
    axes[1, 1].grid(True)
    
    # 流动性评分
    axes[1, 2].plot(features.index, features['liquidity_score'])
    axes[1, 2].set_title('流动性评分')
    axes[1, 2].set_ylabel('评分')
    axes[1, 2].grid(True)
    
    plt.tight_layout()
    plt.savefig('quantitative_features_examples.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 运行测试
    features = test_all_features()
    
    # 绘制示例图表
    print("\n生成特征示例图表...")
    plot_feature_examples(features)
    print("图表已保存为 'quantitative_features_examples.png'")
    
    # 保存特征数据示例
    features.tail(10).to_csv('features_sample.csv')
    print("特征数据示例已保存为 'features_sample.csv'")
