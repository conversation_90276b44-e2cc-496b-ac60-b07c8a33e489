"""
量化交易训练数据生成脚本 - 重构版
按标的分别生成训练/验证/测试数据文件
集成独立的技术指标模块和量化特征工程
"""
import pandas as pd
import numpy as np
import os
from typing import Tuple, List, Dict, Optional
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from quant.tdx_data import get_all_symbols, get_data
from technical_indicators import *
from quantitative_features import *


class QuantDataGenerator:
    """量化数据生成器 - 按标的分别生成数据文件"""

    def __init__(self, lookback_days: int = 60, predict_days: int = 5, test_years: int = 2, save_folder: str = r'D:\L2_DATA_T0_ETF\train_data\RAW\daily_predict'):
        """
        初始化数据生成器

        Args:
            lookback_days: 历史数据天数
            predict_days: 预测天数
            test_years: 最后几年用于验证/测试
        """
        self.lookback_days = lookback_days
        self.predict_days = predict_days
        self.test_years = max(2, test_years)# 至少2年
        self.save_folder = save_folder

        self.base_date = datetime(1990, 1, 1)  # 基准日期
        self.latest_date = self._get_latest_trade_date()# 最新交易日期

        # 创建输出目录
        os.makedirs(os.path.join(self.save_folder,'train'), exist_ok=True)
        os.makedirs(os.path.join(self.save_folder,'val'), exist_ok=True)
        os.makedirs(os.path.join(self.save_folder,'test'), exist_ok=True)
        
    def _get_latest_trade_date(self) -> datetime:
        """获取上证指数的最新交易日"""
        sh_index = get_data('sh000001')
        if sh_index is None or len(sh_index) == 0:
            # 如果无法获取数据，使用当前日期
            return datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

        latest_date_int = sh_index['datetime'].max()
        # int 转为 datetime
        latest_date = datetime.strptime(str(latest_date_int), '%Y%m%d')
        return latest_date

    def filter_symbols(self) -> pd.DataFrame:
        """筛选符合条件的证券"""
        all_symbols = get_all_symbols()
        
        # 限制证券类型
        allowed_types = ['SH_A_STOCK', 'SZ_A_STOCK', 'SH_INDEX', 'SZ_INDEX', 'SH_FUND', 'SZ_FUND']
        filtered_symbols = all_symbols[all_symbols['security_type'].isin(allowed_types)]
        
        print(f"筛选后的证券数量: {len(filtered_symbols)}")
        return filtered_symbols
    
    def add_date_features(self, df: pd.DataFrame, code: str) -> pd.DataFrame:
        """添加日期相关特征"""
        data = df.copy()

        # 转换日期格式并计算日期数
        data['date'] = pd.to_datetime(data['datetime'].astype(str))
        data['date_num'] = (data['date'] - self.base_date).dt.days

        # 生成ID列
        data['id'] = data.apply(lambda row: f"{code}_{row['date_num']}", axis=1)

        return data

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标特征 - 重构版
        使用独立的技术指标函数，包含标准化处理
        """
        data = df.copy()

        # 基础价格特征
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))

        # === 技术指标 (使用独立函数) ===

        # RSI (0-100 -> 标准化为 [-1,1])
        rsi_raw = calculate_rsi(data['close'])
        data['rsi'] = (rsi_raw - 50) / 50

        # 布林带位置 (0-1，天然标准化)
        data['bb_position'] = calculate_bb_position(data['close'])

        # 移动平均比率 (标准化为对数)
        ma_ratios = calculate_moving_average_ratios(data['close'])
        for col in ma_ratios.columns:
            data[f'{col}_norm'] = np.log(ma_ratios[col] + 1e-8)

        # ATR比率 (已标准化)
        data['atr_ratio_norm'] = calculate_atr_ratio(data['high'], data['low'], data['close'])

        # 成交量比率 (对数标准化)
        volume_ratio_raw = calculate_volume_ratio(data['volume'])
        data['volume_ratio_norm'] = np.log(1 + volume_ratio_raw)

        # 成交额变化率
        data['amount_ratio'] = data['amount'].pct_change()

        # 波动率 (标准化)
        volatility_raw = calculate_volatility(data['returns'])
        data['volatility_norm'] = volatility_raw / (volatility_raw.rolling(252).mean() + 1e-8)

        # 日内波动比率 (标准化)
        high_low_ratio_raw = calculate_high_low_ratio(data['high'], data['low'], data['close'])
        data['high_low_ratio_norm'] = high_low_ratio_raw / (high_low_ratio_raw.rolling(252).mean() + 1e-8)

        return data

    def calculate_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算高级量化特征
        基于最新量化研究的最佳实践
        """
        data = df.copy()

        # === 高级技术指标 ===

        # MACD (标准化)
        macd_line, signal_line, histogram = calculate_macd(data['close'])
        data['macd_norm'] = np.tanh(macd_line / (macd_line.rolling(252).std() + 1e-8))
        data['macd_signal_norm'] = np.tanh(signal_line / (signal_line.rolling(252).std() + 1e-8))
        data['macd_hist_norm'] = np.tanh(histogram / (histogram.rolling(252).std() + 1e-8))

        # Williams %R (标准化为 [-1,1])
        willr_raw = calculate_williams_r(data['high'], data['low'], data['close'])
        data['willr_norm'] = (willr_raw + 50) / 50

        # 动量指标 (标准化)
        momentum_raw = calculate_momentum(data['close'])
        data['momentum_norm'] = np.tanh(momentum_raw / (momentum_raw.rolling(252).std() + 1e-8))

        # EMA比率 (对数标准化)
        ema_ratios = calculate_ema_ratios(data['close'])
        for col in ema_ratios.columns:
            data[f'{col}_norm'] = np.log(ema_ratios[col] + 1e-8)

        # ADX (标准化为 [0,1])
        adx_raw = calculate_adx(data['high'], data['low'], data['close'])
        data['adx_norm'] = adx_raw / 100

        # === 市场结构特征 ===

        # 价格和成交量排名 (天然标准化)
        data['price_rank'] = calculate_price_rank(data['close'])
        data['volume_rank'] = calculate_volume_rank(data['volume'])

        # 多周期动量
        momentum_df = calculate_price_momentum_multiple_periods(data['close'])
        for col in momentum_df.columns:
            momentum_std = momentum_df[col].rolling(252).std() + 1e-8
            data[f'{col}_norm'] = np.tanh(momentum_df[col] / momentum_std)

        # 收益率统计特征 (标准化)
        data['return_skew_norm'] = np.tanh(calculate_return_skewness(data['returns']) / 2)
        data['return_kurt_norm'] = np.tanh((calculate_return_kurtosis(data['returns']) - 3) / 3)

        # 波动率状态 (对数标准化)
        vol_regime = calculate_volatility_regime(data['returns'])
        data['vol_regime_norm'] = np.log(vol_regime + 1e-8)

        # 均值回归信号 (天然标准化)
        data['mean_reversion_signal'] = calculate_mean_reversion_signal(data['close'])

        # 趋势强度 (天然标准化)
        data['trend_strength'] = calculate_trend_strength(data['close'])

        # 支撑阻力位距离 (相对距离，天然标准化)
        support_dist, resistance_dist = calculate_support_resistance_levels(data['close'])
        data['support_distance'] = support_dist
        data['resistance_distance'] = resistance_dist

        # 流动性评分 (天然标准化)
        data['liquidity_score'] = calculate_liquidity_score(data['volume'], data['close'])

        return data

    def calculate_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算预测标签"""
        data = df.copy()
        
        # 未来5日收益率
        data['future_return'] = (data['close'].shift(-self.predict_days) / data['close'] - 1)
        
        # 效率比率 (Efficiency Ratio)
        future_close = data['close'].shift(-self.predict_days)
        net_change = abs(future_close - data['close'])
        # 计算未来5天的总价格变动
        total_change = pd.Series(0.0, index=data.index)
        for i in range(1, self.predict_days + 1):
            total_change += abs(data['close'].shift(-i) - data['close'].shift(-i+1))
        data['efficiency_ratio'] = net_change / (total_change + 1e-8)
        
        # 未来5日最大上涨
        # 计算未来5天内的最高价
        future_highs = []
        for i in range(1, self.predict_days + 1):
            future_highs.append(data['high'].shift(-i))
        future_high_df = pd.concat(future_highs, axis=1)
        max_future_high = future_high_df.max(axis=1)
        data['max_upside'] = (max_future_high / data['close'] - 1)
        
        # 未来5日最大回撤
        future_prices = []
        for i in range(1, self.predict_days + 1):
            future_prices.append(data['close'].shift(-i))
        future_df = pd.concat(future_prices, axis=1)
        running_max = future_df.expanding(axis=1).max()
        drawdowns = (future_df - running_max) / running_max
        data['max_drawdown'] = drawdowns.min(axis=1)
        
        return data
    
    def filter_inactive_stocks(self, df: pd.DataFrame, min_price_change: float = 0.05) -> bool:
        """过滤价格基本无变动的标的"""
        if len(df) < self.lookback_days:
            return False
            
        # 使用全部的数据
        price_std = df['close'].std()
        price_mean = df['close'].mean()
        cv = price_std / price_mean  # 变异系数
        
        return cv > min_price_change
    
    def prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """
        准备特征数据 - 重构版
        包含OHLCVA基础特征和标准化的衍生特征
        """
        feature_cols = [
            # OHLCVA基础特征
            'open', 'high', 'low', 'close', 'volume', 'amount',
            # 日期特征
            'date_num',
            # 基础价格特征
            'returns', 'log_returns',

            # === 标准化技术指标 ===
            'rsi',  # 已标准化为 [-1,1]
            'bb_position',  # 天然标准化 [0,1]
            'ma_ratio_5_norm', 'ma_ratio_10_norm', 'ma_ratio_20_norm', 'ma_ratio_30_norm',  # 对数标准化
            'atr_ratio_norm',  # 已标准化
            'volume_ratio_norm',  # 对数标准化
            'amount_ratio',
            'volatility_norm',  # 已标准化
            'high_low_ratio_norm',  # 已标准化

            # === 高级技术指标 ===
            'macd_norm', 'macd_signal_norm', 'macd_hist_norm',  # MACD系列
            'willr_norm',  # Williams %R
            'momentum_norm',  # 动量
            'ema_ratio_5_norm', 'ema_ratio_10_norm', 'ema_ratio_20_norm', 'ema_ratio_30_norm',  # EMA比率
            'adx_norm',  # ADX

            # === 市场结构特征 ===
            'price_rank', 'volume_rank',  # 排名特征
            'momentum_5d_norm', 'momentum_10d_norm', 'momentum_20d_norm', 'momentum_60d_norm',  # 多周期动量
            'return_skew_norm', 'return_kurt_norm',  # 收益率统计特征
            'vol_regime_norm',  # 波动率状态
            'mean_reversion_signal',  # 均值回归信号
            'trend_strength',  # 趋势强度
            'support_distance', 'resistance_distance',  # 支撑阻力位
            'liquidity_score'  # 流动性评分
        ]

        # 只选择存在的列
        available_cols = [col for col in feature_cols if col in df.columns]
        features = df[available_cols].values
        return features
    
    def create_sequences_with_indices(self, features: np.ndarray, labels: np.ndarray, ids: List[str]) -> Tuple[List[Tuple], List[str], List[np.ndarray]]:
        """创建时间序列数据，返回切片索引、ID和标签"""
        slice_indices = []  # 存储特征切片的起始和结束索引
        sequence_ids = []   # 存储对应的ID
        sequence_labels = [] # 存储对应的标签

        for i in range(self.lookback_days, len(features) - self.predict_days):
            # 存储切片索引而不是实际数据
            slice_indices.append((i - self.lookback_days, i))
            # 使用最近一天（第i-1天）的ID和标签
            sequence_ids.append(ids[i-1])
            sequence_labels.append(labels[i-1])

        return slice_indices, sequence_ids, sequence_labels
    
    def split_data_by_time(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """基于日期分割数据，允许有些数据只有训练集，没有测试集"""
        df_sorted = df.sort_values('date').reset_index(drop=True)

        if len(df_sorted) == 0:
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        # 获取数据的日期范围
        min_date = df_sorted['date'].min()
        max_date = df_sorted['date'].max()

        # 计算测试集和验证集的起始日期（从最新日期向前推算）

        # 测试集：最后1年
        test_start_date = self.latest_date - timedelta(days=365)
        # 验证集：根据test_years参数确定
        val_start_date = self.latest_date - timedelta(days=365 * self.test_years)

        # 基于日期条件分割数据
        train_df = df_sorted[df_sorted['date'] < val_start_date].copy()
        val_df = df_sorted[(df_sorted['date'] >= val_start_date) & (df_sorted['date'] < test_start_date)].copy()
        test_df = df_sorted[df_sorted['date'] >= test_start_date].copy()

        print(f"  数据分割 (test_years={self.test_years}):")
        print(f"    数据范围: {min_date.date()} 到 {max_date.date()} (共{len(df_sorted)}天)")
        print(f"    训练集: {train_df['date'].min().date() if len(train_df) > 0 else 'N/A'} 到 {train_df['date'].max().date() if len(train_df) > 0 else 'N/A'} ({len(train_df)}天)")
        print(f"    验证集: {val_df['date'].min().date() if len(val_df) > 0 else 'N/A'} 到 {val_df['date'].max().date() if len(val_df) > 0 else 'N/A'} ({len(val_df)}天)")
        print(f"    测试集: {test_df['date'].min().date() if len(test_df) > 0 else 'N/A'} 到 {test_df['date'].max().date() if len(test_df) > 0 else 'N/A'} ({len(test_df)}天)")

        return train_df, val_df, test_df

    def calculate_normalization_stats(self, train_features: np.ndarray) -> Dict:
        """基于训练数据计算标准化统计量"""
        scaler = StandardScaler()
        scaler.fit(train_features.reshape(-1, train_features.shape[-1]))

        return {
            'mean': scaler.mean_,
            'scale': scaler.scale_,
            'var': scaler.var_
        }

    def process_single_symbol(self, symbol_info: pd.Series) -> bool:
        """处理单个标的数据 - 先分割原始数据，再分别计算特征"""
        try:
            code = symbol_info['code']
            print(f"处理标的: {code}")

            # 1. 获取原始数据
            raw_df = get_data(code, fq=2)

            if raw_df is None or len(raw_df) < self.lookback_days + self.predict_days:
                print(f"  数据不足(不能产生至少一个样本)，跳过")
                return False

            # 添加基础日期信息
            raw_df['date'] = pd.to_datetime(raw_df['datetime'].astype(str))
            raw_df = raw_df.sort_values('date')

            # 过滤不活跃的股票
            # 如 货币etf， 价格始终再 100 小幅波动
            if not self.filter_inactive_stocks(raw_df):
                print(f"  价格变动不足，跳过")
                return False

            # 2. 先分割原始数据
            train_raw, val_raw, test_raw = self.split_data_by_time(raw_df)

            # 不需要删除，有数据都需要利用
            # if len(train_raw) < self.lookback_days or len(val_raw) < self.predict_days or len(test_raw) < self.predict_days:
            #     print(f"  分割后数据不足，跳过")
            #     return False

            # 3. 分别处理每个分割
            datasets = {
                'train': (train_raw, os.path.join(self.save_folder, f'train')),
                'val': (val_raw, os.path.join(self.save_folder, f'val')),
                'test': (test_raw, os.path.join(self.save_folder, f'test'))
            }

            train_norm_stats = None

            for split_name, (raw_data, output_dir) in datasets.items():
                if len(raw_data) < self.lookback_days + self.predict_days:
                    continue

                # 添加日期特征
                data_with_dates = self.add_date_features(raw_data, code)

                # 计算基础技术指标
                data_with_tech = self.calculate_technical_indicators(data_with_dates)

                # 计算高级量化特征
                data_with_features = self.calculate_advanced_features(data_with_tech)

                # 计算标签
                data_with_labels = self.calculate_labels(data_with_features)

                # 清理NaN
                clean_data = data_with_labels.dropna()

                if len(clean_data) < self.lookback_days + self.predict_days:
                    print(f"  {split_name}: 清洗后数据不足，跳过")
                    continue

                # 准备特征和标签
                features = self.prepare_features(clean_data)
                labels = clean_data[['future_return', 'efficiency_ratio', 'max_upside', 'max_drawdown']].values
                ids = clean_data['id'].tolist()

                # 计算标准化统计量（仅训练集）
                if split_name == 'train':
                    train_norm_stats = self.calculate_normalization_stats(features)

                # 创建序列
                slice_indices, sequence_ids, sequence_labels = self.create_sequences_with_indices(
                    features, labels, ids
                )

                if len(slice_indices) > 0:
                    # 保存数据文件
                    output_file = os.path.join(output_dir, f"{code}.npz")
                    np.savez_compressed(
                        output_file,
                        slice_indices=np.array(slice_indices),
                        ids=np.array(sequence_ids),
                        labels=np.array(sequence_labels),
                        norm_stats=train_norm_stats if train_norm_stats else {},
                        features_full=features
                    )
                    print(f"  {split_name}: {len(slice_indices)} 样本")

            return True

        except Exception as e:
            print(f"处理 {symbol_info['code']} 时出错: {e}")
            return False


    def generate_all_data(self):
        """生成所有标的的数据文件"""
        symbols = self.filter_symbols()
        symbols = symbols[:10]

        success_count = 0
        total_count = len(symbols)

        print(f"开始处理 {total_count} 个标的...")

        for _, symbol_info in symbols.iterrows():
            if self.process_single_symbol(symbol_info):
                success_count += 1

        print(f"\n数据生成完成！")
        print(f"成功处理: {success_count}/{total_count} 个标的")
        print(f"数据文件保存在: data/train/, data/val/, data/test/")


if __name__ == "__main__":
    # 生成按标的分类的训练数据
    generator = QuantDataGenerator(
        lookback_days=60, 
        predict_days=5, 
        test_years=2, 
        save_folder=r'D:\L2_DATA_T0_ETF\train_data\RAW\daily_predict'
    )
    generator.generate_all_data()
