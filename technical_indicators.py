"""
技术指标计算模块
将各个技术指标拆分为独立函数，便于测试和维护
"""
import pandas as pd
import numpy as np
from typing import Union, Tuple, Optional


def calculate_rsi(close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算相对强弱指数 (RSI)

    RSI是衡量价格变动速度和变化的动量振荡器，
    通过比较一段时期内的平均收盘涨数和平均收盘跌数来分析市场买卖盘的意向和实力。

    Args:
        close: 收盘价序列
        window: 计算窗口，默认14天

    Returns:
        RSI值序列，范围 0-100

    标准化建议:
        (rsi - 50) / 50，将范围转换为 [-1, 1]

    应用场景:
        - 超买超卖：RSI > 70为超买，RSI < 30为超卖
        - 背离分析：价格与RSI走势背离时的反转信号
        - 趋势确认：RSI突破50线确认趋势方向
    """
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / (loss + 1e-8)  # 避免除零
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_bollinger_bands(close: pd.Series, window: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    计算布林带

    布林带由三条线组成：中轨（移动平均线）、上轨和下轨（中轨±标准差）。
    布林带宽度反映市场波动性，价格触及上下轨时可能出现反转。

    Args:
        close: 收盘价序列
        window: 移动平均窗口，默认20天
        std_dev: 标准差倍数，默认2.0倍

    Returns:
        (上轨, 中轨, 下轨) 的元组

    布林带位置计算:
        (close - lower) / (upper - lower)，范围 0-1，天然标准化

    应用场景:
        - 支撑阻力：上下轨作为动态支撑阻力位
        - 波动率分析：带宽收窄预示突破，带宽扩张表示高波动
        - 均值回归：价格偏离中轨过远时的回归交易机会
    """
    middle = close.rolling(window).mean()
    std = close.rolling(window).std()
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    return upper, middle, lower


def calculate_bb_position(close: pd.Series, window: int = 20, std_dev: float = 2.0) -> pd.Series:
    """
    计算布林带位置

    布林带位置指标用于衡量当前价格在布林带中的相对位置，
    0表示价格在下轨，1表示价格在上轨，0.5表示价格在中轨。

    Args:
        close: 收盘价序列
        window: 移动平均窗口，默认20
        std_dev: 标准差倍数，默认2.0

    Returns:
        布林带位置序列，范围 0-1，天然标准化

    应用场景:
        - 超买超卖判断：接近1时超买，接近0时超卖
        - 趋势强度：持续在0.8以上或0.2以下表示强趋势
    """
    upper, _, lower = calculate_bollinger_bands(close, window, std_dev)
    position = (close - lower) / (upper - lower + 1e-8)
    return position.clip(0, 1)  # 确保在 [0,1] 范围内


def calculate_moving_average_ratios(close: pd.Series, windows: list = [5, 10, 20, 30]) -> pd.DataFrame:
    """
    计算多周期移动平均比率

    移动平均比率反映当前价格相对于不同周期均线的位置关系，
    可以同时观察短期、中期、长期趋势的强弱。

    Args:
        close: 收盘价序列
        windows: 移动平均窗口列表，默认[5, 10, 20, 30]

    Returns:
        包含各周期MA比率的DataFrame，列名格式为'ma_ratio_{window}'

    标准化建议:
        np.log(ratio)，因为比率通常在 0.5-2.0 范围内

    应用场景:
        - 趋势强度：比率>1表示价格在均线之上，趋势向上
        - 多时间框架分析：不同周期比率的组合判断趋势一致性
        - 动量分析：比率变化速度反映价格动量强弱
    """
    ratios = pd.DataFrame(index=close.index)
    for window in windows:
        ma = close.rolling(window).mean()
        ratios[f'ma_ratio_{window}'] = close / ma
    return ratios


def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算平均真实波幅 (ATR)

    ATR衡量价格波动的幅度，考虑了跳空因素，
    是衡量市场波动性的重要指标，常用于止损位设置和仓位管理。

    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        window: 计算窗口，默认14天

    Returns:
        ATR值序列（绝对价格单位）

    ATR比率:
        atr / close，范围通常 0-0.1
        标准化建议: atr_ratio / rolling_mean(atr_ratio, 252)

    应用场景:
        - 止损设置：ATR的1-3倍作为止损距离
        - 仓位管理：高ATR时减少仓位规模
        - 波动率过滤：ATR突然放大时谨慎交易
    """
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(window).mean()
    return atr


def calculate_atr_ratio(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算ATR比率 (标准化的ATR)

    ATR比率是ATR与收盘价的比值，反映了相对波动率。
    通过252日滚动均值进行标准化，使指标更具可比性。

    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        window: ATR计算窗口，默认14

    Returns:
        标准化的ATR比率序列

    应用场景:
        - 波动率比较：不同股票间的波动率对比
        - 风险管理：高ATR比率时减少仓位
        - 止损设置：根据ATR比率动态调整止损距离
    """
    atr = calculate_atr(high, low, close, window)
    atr_ratio = atr / close
    # 使用252日滚动均值进行标准化
    normalized_atr = atr_ratio / (atr_ratio.rolling(252).mean() + 1e-8)
    return normalized_atr


def calculate_volume_ratio(volume: pd.Series, window: int = 20) -> pd.Series:
    """
    计算成交量比率

    成交量比率是当前成交量与历史平均成交量的比值，
    反映市场参与度和资金流入流出的活跃程度。

    Args:
        volume: 成交量序列
        window: 移动平均窗口，默认20天

    Returns:
        成交量比率序列，范围通常 0-10

    标准化建议:
        np.log(1 + volume_ratio) 进行对数变换

    应用场景:
        - 突破确认：价格突破伴随高成交量比率更可靠
        - 趋势强度：持续的高成交量比率表示强趋势
        - 反转信号：极端高成交量比率可能预示反转
    """
    volume_ma = volume.rolling(window).mean()
    volume_ratio = volume / (volume_ma + 1e-8)
    return volume_ratio


def calculate_volatility(returns: pd.Series, window: int = 20) -> pd.Series:
    """
    计算滚动波动率（年化）

    滚动波动率衡量价格收益率的标准差，反映价格变动的不确定性。
    年化处理使得不同时间段的波动率具有可比性。

    Args:
        returns: 收益率序列（通常为日收益率）
        window: 计算窗口，默认20天

    Returns:
        年化波动率序列，范围通常 0-1（0-100%）

    标准化建议:
        volatility / rolling_mean(volatility, 252) 进行相对标准化

    应用场景:
        - 风险评估：高波动率资产需要更大的风险缓冲
        - 期权定价：波动率是期权价值的重要输入
        - 资产配置：根据波动率调整资产权重
    """
    volatility = returns.rolling(window).std()
    # 年化波动率
    annualized_vol = volatility * np.sqrt(252)
    return annualized_vol


def calculate_high_low_ratio(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
    """
    计算日内波动比率

    日内波动比率衡量单日内价格波动幅度相对于收盘价的比例，
    反映了市场的日内活跃程度和波动性。

    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列

    Returns:
        日内波动比率序列，范围通常 0-0.2

    标准化建议:
        ratio / rolling_mean(ratio, 252) 进行相对标准化

    应用场景:
        - 波动率预测：高比率预示后续可能的高波动
        - 交易时机：低波动时期可能是突破前的蓄势
        - 风险控制：高波动时期需要更严格的风险管理
    """
    ratio = (high - low) / close
    return ratio


def calculate_macd(close: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    计算MACD指标（指数平滑移动平均收敛发散）

    MACD是趋势跟踪动量指标，通过快慢EMA的差值来判断趋势变化。
    MACD线上穿信号线为买入信号，下穿为卖出信号。

    Args:
        close: 收盘价序列
        fast: 快线EMA周期，默认12天
        slow: 慢线EMA周期，默认26天
        signal: 信号线EMA周期，默认9天

    Returns:
        (MACD线, 信号线, 柱状图) 的元组

    标准化建议:
        np.tanh(macd / rolling_std(macd, 252)) 使用双曲正切函数

    应用场景:
        - 趋势确认：MACD线方向确认价格趋势
        - 买卖信号：MACD线与信号线的金叉死叉
        - 背离分析：价格与MACD走势背离的反转信号
    """
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


def calculate_williams_r(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算威廉指标 (%R)

    威廉指标是动量指标，衡量收盘价在过去N天最高价和最低价区间中的位置。
    与随机指标类似，但计算方法相反，用于识别超买超卖状态。

    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        window: 计算窗口，默认14天

    Returns:
        Williams %R值序列，范围 -100 到 0

    标准化建议:
        (willr + 50) / 50，将范围转换为 [-1, 1]

    应用场景:
        - 超买超卖：%R > -20为超买，%R < -80为超卖
        - 背离分析：价格与%R走势背离时的反转机会
        - 趋势确认：%R突破-50线确认趋势方向
    """
    highest_high = high.rolling(window).max()
    lowest_low = low.rolling(window).min()
    willr = -100 * (highest_high - close) / (highest_high - lowest_low + 1e-8)
    return willr


def calculate_momentum(close: pd.Series, window: int = 10) -> pd.Series:
    """
    计算动量指标

    动量指标衡量价格在指定时间段内的变化幅度，
    反映价格变动的速度和力度，是最简单的趋势指标之一。

    Args:
        close: 收盘价序列
        window: 计算窗口，默认10天

    Returns:
        动量值序列（价格差值）

    标准化建议:
        np.tanh(momentum / rolling_std(momentum, 252)) 使用双曲正切函数

    应用场景:
        - 趋势强度：正动量表示上涨趋势，负动量表示下跌趋势
        - 动量背离：价格新高但动量下降预示趋势减弱
        - 零轴交叉：动量穿越零轴作为趋势转换信号
    """
    momentum = close - close.shift(window)
    return momentum


def calculate_ema_ratios(close: pd.Series, windows: list = [5, 10, 20, 30]) -> pd.DataFrame:
    """
    计算多周期EMA比率

    EMA比率反映当前价格相对于不同周期指数移动平均线的位置关系。
    EMA对近期价格变化更敏感，比SMA能更快反映趋势变化。

    Args:
        close: 收盘价序列
        windows: EMA窗口列表，默认[5, 10, 20, 30]

    Returns:
        包含各周期EMA比率的DataFrame，列名格式为'ema_ratio_{window}'

    标准化建议:
        np.log(ratio) 进行对数变换

    应用场景:
        - 趋势识别：比率>1表示价格在EMA之上，趋势向上
        - 多时间框架：不同周期EMA比率组合判断趋势强度
        - 动量分析：EMA比率变化速度反映价格动量
    """
    ratios = pd.DataFrame(index=close.index)
    for window in windows:
        ema = close.ewm(span=window).mean()
        ratios[f'ema_ratio_{window}'] = close / ema
    return ratios


def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算平均趋向指数 (ADX)

    ADX衡量趋势的强度，而不是趋势的方向。
    基于方向移动指标(DI+和DI-)计算，值越高表示趋势越强。

    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        window: 计算窗口，默认14天

    Returns:
        ADX值序列，范围 0-100

    标准化建议:
        adx / 100，将范围转换为 [0, 1]

    应用场景:
        - 趋势强度：ADX > 25表示强趋势，ADX < 20表示弱趋势
        - 趋势过滤：只在强趋势时进行趋势跟踪交易
        - 市场状态：ADX上升表示趋势加强，下降表示趋势减弱

    注意事项:
        ADX只衡量趋势强度，需结合DI+和DI-判断趋势方向
    """
    # 计算真实波幅
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算方向移动
    dm_plus = np.where((high - high.shift(1)) > (low.shift(1) - low), 
                       np.maximum(high - high.shift(1), 0), 0)
    dm_minus = np.where((low.shift(1) - low) > (high - high.shift(1)), 
                        np.maximum(low.shift(1) - low, 0), 0)
    
    dm_plus = pd.Series(dm_plus, index=high.index)
    dm_minus = pd.Series(dm_minus, index=high.index)
    
    # 计算平滑的DI
    tr_smooth = tr.rolling(window).mean()
    dm_plus_smooth = dm_plus.rolling(window).mean()
    dm_minus_smooth = dm_minus.rolling(window).mean()
    
    di_plus = 100 * dm_plus_smooth / tr_smooth
    di_minus = 100 * dm_minus_smooth / tr_smooth
    
    # 计算ADX
    dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus + 1e-8)
    adx = dx.rolling(window).mean()
    
    return adx
