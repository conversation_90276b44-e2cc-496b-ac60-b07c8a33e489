"""
量化特征工程模块
包含高级量化因子和市场结构特征

本模块提供了一套完整的量化特征计算函数，涵盖：
- 价格和成交量排名特征
- 风险调整收益指标
- 市场微观结构特征
- 技术分析指标
- 流动性和市值因子

所有函数都经过优化，支持大规模时间序列数据处理，
并提供了标准化建议以便于机器学习模型使用。
"""
import pandas as pd
import numpy as np
from typing import Union, Tuple, Optional, List


def calculate_price_rank(close: pd.Series, window: int = 252) -> pd.Series:
    """
    计算价格分位数排名 - 反映当前价格在历史区间中的相对位置

    这是一个重要的均值回归信号，当价格排名接近1时表示价格处于历史高位，
    可能面临回调压力；当排名接近0时表示价格处于历史低位，可能存在反弹机会。

    Args:
        close (pd.Series): 收盘价序列，建议使用复权价格
        window (int): 排名窗口期，默认252日(一年)
                     - 短期：60日(季度) - 适合短线交易
                     - 中期：252日(年度) - 适合中长线策略
                     - 长期：1260日(5年) - 适合价值投资

    Returns:
        pd.Series: 价格排名，范围 0-1，天然标准化
                  - 0.8-1.0: 历史高位区间，关注卖出信号
                  - 0.2-0.8: 正常波动区间
                  - 0.0-0.2: 历史低位区间，关注买入信号

    Usage:
        >>> # 基本用法
        >>> price_rank = calculate_price_rank(df['close'])
        >>>
        >>> # 多周期组合
        >>> short_rank = calculate_price_rank(df['close'], window=60)
        >>> long_rank = calculate_price_rank(df['close'], window=252)
        >>> rank_divergence = short_rank - long_rank  # 短长期排名差异

    Note:
        - 该指标对异常值不敏感，适合处理有跳空的数据
        - 建议与趋势指标结合使用，避免在强趋势中逆势操作
        - 在震荡市中效果最佳
    """
    rank = close.rolling(window).rank(pct=True)
    return rank


def calculate_volume_rank(volume: pd.Series, window: int = 252) -> pd.Series:
    """
    计算成交量分位数排名 - 识别异常交易活跃度

    成交量排名可以识别市场情绪的变化，高排名通常伴随重要的价格变动，
    是确认价格突破和反转的重要指标。

    Args:
        volume (pd.Series): 成交量序列，单位可以是股数或成交额
        window (int): 排名窗口期，默认252日
                     - 建议与价格排名使用相同窗口期以保持一致性

    Returns:
        pd.Series: 成交量排名，范围 0-1，天然标准化
                  - 0.9-1.0: 极高成交量，关注重大事件或转折点
                  - 0.7-0.9: 高成交量，价格变动可能持续
                  - 0.3-0.7: 正常成交量区间
                  - 0.0-0.3: 低成交量，价格变动可信度较低

    Usage:
        >>> # 基本用法
        >>> vol_rank = calculate_volume_rank(df['volume'])
        >>>
        >>> # 结合价格排名使用
        >>> price_rank = calculate_price_rank(df['close'])
        >>> vol_rank = calculate_volume_rank(df['volume'])
        >>>
        >>> # 识别放量突破
        >>> breakout_signal = (price_rank > 0.8) & (vol_rank > 0.7)
        >>>
        >>> # 识别缩量下跌（可能是假跌破）
        >>> false_breakdown = (price_rank < 0.2) & (vol_rank < 0.3)

    Note:
        - 成交量数据质量对结果影响较大，建议预处理异常值
        - 在除权除息日附近可能出现异常，需要特别处理
        - 与价格变动方向结合分析效果更佳
    """
    rank = volume.rolling(window).rank(pct=True)
    return rank


def calculate_return_skewness(returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算收益率偏度 - 衡量收益率分布的不对称性

    偏度是风险管理的重要指标，负偏度表示存在较大下跌风险，
    正偏度表示存在较大上涨潜力。在投资组合构建中，
    偏度可以用来识别尾部风险和机会。

    Args:
        returns (pd.Series): 日收益率序列，建议使用对数收益率
                           计算方式: np.log(close / close.shift(1))
        window (int): 滚动计算窗口，默认60日(约3个月)
                     - 短期：20日 - 捕捉短期情绪变化
                     - 中期：60日 - 平衡稳定性和敏感性
                     - 长期：120日 - 更稳定的长期特征

    Returns:
        pd.Series: 收益率偏度
                  - 偏度 > 0.5: 正偏，上涨概率较大，但要警惕回调
                  - -0.5 < 偏度 < 0.5: 接近正态分布
                  - 偏度 < -0.5: 负偏，下跌风险较大

    范围: 通常 -3 到 3
    标准化建议: np.tanh(skewness / 2)

    Usage:
        >>> # 基本用法
        >>> returns = np.log(df['close'] / df['close'].shift(1))
        >>> skew = calculate_return_skewness(returns)
        >>>
        >>> # 风险预警信号
        >>> risk_warning = skew < -1.0  # 强负偏度预警
        >>>
        >>> # 结合其他指标
        >>> vol = returns.rolling(60).std()
        >>> risk_score = -skew * vol  # 综合风险评分

    Note:
        - 偏度计算需要足够的样本量，建议window >= 30
        - 在市场极端情况下偏度可能失效
        - 建议与峰度指标结合使用，全面评估分布特征
    """
    skewness = returns.rolling(window).skew()
    return skewness


def calculate_return_kurtosis(returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算收益率峰度 - 衡量收益率分布的尖峭程度和尾部厚度

    峰度反映了极端收益率出现的频率，高峰度意味着更频繁的极端事件，
    这在风险管理和期权定价中非常重要。正常分布的峰度为3。

    Args:
        returns (pd.Series): 日收益率序列，建议使用对数收益率
        window (int): 滚动计算窗口，默认60日
                     - 峰度计算对样本量要求较高，建议window >= 60

    Returns:
        pd.Series: 收益率峰度（超额峰度，即减去3后的值）
                  - 峰度 > 3: 尖峭分布，极端事件频发
                  - 峰度 ≈ 3: 接近正态分布
                  - 峰度 < 3: 平坦分布，极端事件较少

    范围: 通常 1 到 10
    标准化建议: np.tanh((kurtosis - 3) / 3)

    Usage:
        >>> # 基本用法
        >>> returns = np.log(df['close'] / df['close'].shift(1))
        >>> kurt = calculate_return_kurtosis(returns)
        >>>
        >>> # 识别高波动期
        >>> high_volatility_regime = kurt > 5  # 高峰度期间
        >>>
        >>> # 风险调整
        >>> base_position = 0.1  # 基础仓位
        >>> adjusted_position = base_position / (1 + kurt / 10)  # 峰度调整仓位
        >>>
        >>> # 与偏度结合分析
        >>> skew = calculate_return_skewness(returns)
        >>> tail_risk = (kurt > 4) & (skew < -0.5)  # 高峰度+负偏度=高尾部风险

    Note:
        - 峰度对异常值非常敏感，建议预处理极端值
        - 在计算前确保收益率数据已经去除了明显的异常值
        - 峰度与波动率聚集现象密切相关
    """
    kurtosis = returns.rolling(window).kurt()
    return kurtosis


def calculate_sharpe_ratio(returns: pd.Series, window: int = 60, risk_free_rate: float = 0.02) -> pd.Series:
    """
    计算滚动夏普比率 - 风险调整后收益的经典指标

    夏普比率是衡量投资效率的黄金标准，它考虑了收益和风险的平衡。
    高夏普比率表示单位风险下获得更高收益，是选股和择时的重要参考。

    Args:
        returns (pd.Series): 日收益率序列
        window (int): 滚动计算窗口，默认60日
                     - 短期：30日 - 适合短线策略评估
                     - 中期：60日 - 平衡稳定性和及时性
                     - 长期：252日 - 年度表现评估
        risk_free_rate (float): 年化无风险利率，默认2%
                               - 可根据当前市场环境调整
                               - 建议使用同期国债收益率

    Returns:
        pd.Series: 年化夏普比率
                  - 夏普比率 > 1.0: 优秀表现
                  - 0.5 < 夏普比率 < 1.0: 良好表现
                  - 0 < 夏普比率 < 0.5: 一般表现
                  - 夏普比率 < 0: 表现不如无风险资产

    范围: 通常 -2 到 3
    标准化建议: np.tanh(sharpe / 2)

    Usage:
        >>> # 基本用法
        >>> returns = df['close'].pct_change()
        >>> sharpe = calculate_sharpe_ratio(returns)
        >>>
        >>> # 动态风险调整
        >>> current_rf = 0.025  # 当前无风险利率2.5%
        >>> sharpe = calculate_sharpe_ratio(returns, risk_free_rate=current_rf)
        >>>
        >>> # 选股信号
        >>> good_stocks = sharpe > 1.0  # 夏普比率大于1的股票
        >>>
        >>> # 仓位调整
        >>> position_weight = np.clip(sharpe / 2, 0, 1)  # 基于夏普比率的仓位权重

    Note:
        - 夏普比率假设收益率正态分布，在极端市场可能失效
        - 建议与最大回撤等指标结合使用
        - 对于高频策略，需要调整年化因子
    """
    daily_rf = risk_free_rate / 252
    excess_returns = returns - daily_rf
    sharpe = excess_returns.rolling(window).mean() / (returns.rolling(window).std() + 1e-8)
    # 年化
    sharpe_annualized = sharpe * np.sqrt(252)
    return sharpe_annualized


def calculate_max_drawdown_rolling(returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算滚动最大回撤 - 衡量投资组合的最大损失风险

    最大回撤是风险管理的核心指标，它反映了在特定时期内
    从峰值到谷值的最大跌幅。这个指标对于设定止损位和
    评估策略的风险承受能力至关重要。

    Args:
        returns (pd.Series): 日收益率序列
        window (int): 滚动计算窗口，默认60日
                     - 短期：30日 - 快速风险监控
                     - 中期：60日 - 平衡及时性和稳定性
                     - 长期：252日 - 年度风险评估

    Returns:
        pd.Series: 滚动最大回撤（负值）
                  - 回撤 > -5%: 低风险
                  - -10% < 回撤 < -5%: 中等风险
                  - -20% < 回撤 < -10%: 高风险
                  - 回撤 < -20%: 极高风险

    范围: 通常 0 到 -0.5
    标准化建议: -drawdown (转为正值)

    Usage:
        >>> # 基本用法
        >>> returns = df['close'].pct_change()
        >>> drawdown = calculate_max_drawdown_rolling(returns)
        >>>
        >>> # 风险预警
        >>> risk_alert = drawdown < -0.15  # 回撤超过15%预警
        >>>
        >>> # 动态止损
        >>> stop_loss_level = drawdown * 1.5  # 基于历史回撤的止损位
        >>>
        >>> # 风险调整仓位
        >>> risk_factor = np.abs(drawdown)
        >>> adjusted_position = base_position * (1 - risk_factor)
        >>>
        >>> # 与夏普比率结合
        >>> sharpe = calculate_sharpe_ratio(returns)
        >>> risk_adjusted_score = sharpe / (1 + np.abs(drawdown))

    Note:
        - 回撤计算基于复利效应，更准确反映实际损失
        - 在趋势市场中，回撤指标可能滞后
        - 建议结合波动率指标综合评估风险
    """
    def rolling_max_drawdown(ret_series):
        cumulative = (1 + ret_series).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()

    rolling_dd = returns.rolling(window).apply(rolling_max_drawdown, raw=False)
    return rolling_dd


def calculate_beta(returns: pd.Series, market_returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算滚动Beta系数 - 衡量个股相对市场的系统性风险

    Beta系数反映了个股收益率对市场收益率变化的敏感程度，
    是资产定价模型(CAPM)的核心参数，也是风险管理和投资组合构建的重要工具。

    Args:
        returns (pd.Series): 个股日收益率序列
        market_returns (pd.Series): 市场指数日收益率序列
                                   - 建议使用宽基指数如沪深300、中证500
                                   - 确保与个股在同一市场
        window (int): 滚动计算窗口，默认60日
                     - 短期：30日 - 捕捉近期风险特征变化
                     - 中期：60日 - 平衡稳定性和敏感性
                     - 长期：252日 - 稳定的长期Beta估计

    Returns:
        pd.Series: Beta系数
                  - Beta > 1.2: 高Beta股票，市场敏感度高
                  - 0.8 < Beta < 1.2: 中等Beta，与市场同步
                  - Beta < 0.8: 低Beta股票，防御性较强
                  - Beta < 0: 与市场负相关（罕见）

    范围: 通常 0 到 3
    标准化建议: np.tanh((beta - 1) / 1)

    Usage:
        >>> # 基本用法
        >>> stock_returns = df['close'].pct_change()
        >>> market_returns = market_df['close'].pct_change()
        >>> beta = calculate_beta(stock_returns, market_returns)
        >>>
        >>> # 风险分类
        >>> high_beta_stocks = beta > 1.2  # 高风险股票
        >>> defensive_stocks = beta < 0.8  # 防御性股票
        >>>
        >>> # 市场择时策略
        >>> market_up_trend = market_returns.rolling(20).mean() > 0
        >>> # 牛市选高Beta，熊市选低Beta
        >>> preferred_stocks = np.where(market_up_trend, beta > 1.0, beta < 1.0)
        >>>
        >>> # 投资组合Beta调整
        >>> target_portfolio_beta = 1.0
        >>> weight_adjustment = target_portfolio_beta / beta

    Note:
        - Beta计算要求个股和市场数据时间对齐
        - 在市场结构性变化时Beta可能不稳定
        - 建议定期更新Beta估计，特别是在市场环境变化时
        - 对于新上市股票，需要足够的历史数据才能获得可靠的Beta
    """
    # 对齐数据
    aligned_data = pd.concat([returns, market_returns], axis=1).dropna()
    if len(aligned_data.columns) != 2:
        return pd.Series(np.nan, index=returns.index)

    aligned_data.columns = ['stock', 'market']

    # 计算滚动协方差和方差
    rolling_cov = aligned_data['stock'].rolling(window).cov(aligned_data['market'])
    rolling_var = aligned_data['market'].rolling(window).var()

    # 计算Beta
    beta = rolling_cov / (rolling_var + 1e-8)

    # 重新索引到原始索引
    result = pd.Series(np.nan, index=returns.index)
    result.loc[beta.index] = beta

    return result


def calculate_information_ratio(returns: pd.Series, benchmark_returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算信息比率 - 衡量主动管理的风险调整超额收益

    信息比率是评估主动投资策略效果的重要指标，它衡量了
    相对于基准的超额收益与跟踪误差的比值，反映了获得
    单位跟踪风险所对应的超额收益。

    Args:
        returns (pd.Series): 投资组合或个股收益率序列
        benchmark_returns (pd.Series): 基准收益率序列
                                     - 通常使用行业指数或市场指数
                                     - 确保基准的代表性和可投资性
        window (int): 滚动计算窗口，默认60日
                     - 建议根据策略周期调整窗口大小

    Returns:
        pd.Series: 年化信息比率
                  - IR > 0.5: 优秀的主动管理能力
                  - 0.2 < IR < 0.5: 良好的主动管理能力
                  - 0 < IR < 0.2: 一般的主动管理能力
                  - IR < 0: 主动管理产生负价值

    范围: 通常 -2 到 2
    标准化建议: np.tanh(ir / 1)

    Usage:
        >>> # 基本用法
        >>> portfolio_returns = df['close'].pct_change()
        >>> benchmark_returns = benchmark_df['close'].pct_change()
        >>> ir = calculate_information_ratio(portfolio_returns, benchmark_returns)
        >>>
        >>> # 策略评估
        >>> good_strategy = ir > 0.3  # 信息比率大于0.3的策略
        >>>
        >>> # 动态仓位调整
        >>> base_position = 0.1
        >>> ir_adjusted_position = base_position * (1 + np.clip(ir, -1, 1))
        >>>
        >>> # 与夏普比率比较
        >>> sharpe = calculate_sharpe_ratio(portfolio_returns)
        >>> benchmark_sharpe = calculate_sharpe_ratio(benchmark_returns)
        >>> # IR应该与(Sharpe - Benchmark_Sharpe)相关

    Note:
        - 信息比率对基准选择敏感，需要选择合适的基准
        - 高信息比率不一定意味着高绝对收益
        - 建议与其他风险调整指标结合使用
        - 在计算前确保收益率数据的质量和一致性
    """
    excess_returns = returns - benchmark_returns
    tracking_error = excess_returns.rolling(window).std()
    ir = excess_returns.rolling(window).mean() / (tracking_error + 1e-8)
    # 年化
    ir_annualized = ir * np.sqrt(252)
    return ir_annualized


def calculate_turnover_rate(volume: pd.Series, shares_outstanding: Union[float, pd.Series], window: int = 20) -> pd.Series:
    """
    计算换手率 - 衡量股票流动性和市场活跃度的关键指标

    换手率反映了股票的交易活跃程度，高换手率通常伴随着
    价格的大幅波动，是技术分析和量化交易的重要参考指标。

    Args:
        volume (pd.Series): 日成交量序列（股数）
        shares_outstanding (Union[float, pd.Series]): 流通股本
                                                    - 可以是固定值或时间序列
                                                    - 建议使用最新的流通股本数据
        window (int): 平滑窗口，默认20日
                     - 用于平滑日度换手率的波动
                     - 可根据分析需求调整

    Returns:
        pd.Series: 平滑后的换手率
                  - 换手率 > 10%: 极高活跃度，关注异常事件
                  - 5% < 换手率 < 10%: 高活跃度
                  - 2% < 换手率 < 5%: 正常活跃度
                  - 换手率 < 2%: 低活跃度，流动性较差

    范围: 通常 0 到 1（0-100%）
    标准化建议: np.log(1 + turnover)

    Usage:
        >>> # 基本用法
        >>> turnover = calculate_turnover_rate(df['volume'], shares_outstanding=1e8)
        >>>
        >>> # 识别异常交易活动
        >>> abnormal_trading = turnover > turnover.rolling(252).quantile(0.95)
        >>>
        >>> # 流动性筛选
        >>> liquid_stocks = turnover.rolling(60).mean() > 0.02  # 平均换手率>2%
        >>>
        >>> # 与价格变动结合
        >>> price_change = df['close'].pct_change()
        >>> volume_price_signal = (turnover > 0.05) & (abs(price_change) > 0.03)
        >>>
        >>> # 动态流通股本
        >>> # shares_outstanding可以是时间序列，处理股本变化
        >>> dynamic_shares = pd.Series([1e8, 1.1e8, 1.1e8], index=df.index[:3])
        >>> turnover = calculate_turnover_rate(df['volume'], dynamic_shares)

    Note:
        - 换手率计算需要准确的流通股本数据
        - 在除权除息日前后换手率可能异常
        - 建议结合成交额数据进行交叉验证
        - 对于不同市值的股票，换手率的意义可能不同
    """
    if isinstance(shares_outstanding, (int, float)):
        shares_outstanding = pd.Series(shares_outstanding, index=volume.index)

    daily_turnover = volume / shares_outstanding
    smoothed_turnover = daily_turnover.rolling(window).mean()
    return smoothed_turnover


def calculate_price_momentum_multiple_periods(close: pd.Series, periods: List[int] = [5, 10, 20, 60]) -> pd.DataFrame:
    """
    计算多周期价格动量 - 捕捉不同时间尺度的价格趋势

    多周期动量分析可以识别短期、中期和长期的价格趋势，
    帮助构建更稳健的趋势跟踪策略。不同周期的动量组合
    可以提供更丰富的市场信息。

    Args:
        close (pd.Series): 收盘价序列，建议使用复权价格
        periods (List[int]): 动量计算周期列表，默认[5, 10, 20, 60]
                           - 5日: 超短期动量，捕捉日内到周级别趋势
                           - 10日: 短期动量，适合短线交易
                           - 20日: 中短期动量，月度趋势
                           - 60日: 中期动量，季度趋势
                           - 可自定义: [1, 3, 5, 10, 20, 60, 120, 252]

    Returns:
        pd.DataFrame: 包含各周期动量的DataFrame
                     列名格式: 'momentum_Xd'
                     - 正值: 上涨动量
                     - 负值: 下跌动量
                     - 绝对值大小: 动量强度

    标准化建议: np.tanh(momentum / rolling_std(momentum, 252))

    Usage:
        >>> # 基本用法
        >>> momentum_df = calculate_price_momentum_multiple_periods(df['close'])
        >>>
        >>> # 自定义周期
        >>> custom_periods = [1, 5, 20, 60, 252]  # 日、周、月、季、年
        >>> momentum_df = calculate_price_momentum_multiple_periods(
        ...     df['close'], periods=custom_periods
        ... )
        >>>
        >>> # 动量一致性信号
        >>> # 所有周期动量同向为强信号
        >>> all_positive = (momentum_df > 0).all(axis=1)
        >>> all_negative = (momentum_df < 0).all(axis=1)
        >>> strong_trend = all_positive | all_negative
        >>>
        >>> # 动量分歧信号
        >>> short_term = momentum_df['momentum_5d']
        >>> long_term = momentum_df['momentum_60d']
        >>> momentum_divergence = (short_term > 0) & (long_term < 0)  # 短多长空
        >>>
        >>> # 动量强度评分
        >>> momentum_score = momentum_df.mean(axis=1)  # 平均动量
        >>> momentum_strength = momentum_df.abs().mean(axis=1)  # 动量强度
        >>>
        >>> # 标准化处理
        >>> for col in momentum_df.columns:
        ...     rolling_std = momentum_df[col].rolling(252).std()
        ...     momentum_df[f'{col}_normalized'] = np.tanh(momentum_df[col] / rolling_std)

    Note:
        - 动量计算基于简单收益率，适合大多数应用场景
        - 对于长周期动量，建议使用复权价格避免分红影响
        - 在震荡市中，短期动量可能产生较多噪音
        - 建议结合成交量确认动量的有效性
    """
    momentum_df = pd.DataFrame(index=close.index)
    for period in periods:
        momentum = close.pct_change(period)
        momentum_df[f'momentum_{period}d'] = momentum
    return momentum_df


def calculate_volatility_regime(returns: pd.Series, short_window: int = 20, long_window: int = 60) -> pd.Series:
    """
    计算波动率状态指标 - 识别市场波动率的变化趋势

    波动率状态指标通过比较短期和长期波动率来识别市场环境的变化，
    这对于风险管理、仓位调整和策略选择具有重要意义。
    高波动率期间通常伴随着更大的交易机会和风险。

    Args:
        returns (pd.Series): 日收益率序列
        short_window (int): 短期波动率窗口，默认20日
                           - 建议10-30日，捕捉近期波动率变化
        long_window (int): 长期波动率窗口，默认60日
                          - 建议60-120日，作为波动率基准

    Returns:
        pd.Series: 波动率状态比率
                  - 比率 > 1.5: 高波动率状态，市场不稳定
                  - 1.0 < 比率 < 1.5: 波动率上升期
                  - 0.7 < 比率 < 1.0: 波动率下降期
                  - 比率 < 0.7: 低波动率状态，市场相对平静

    范围: 通常 0.5 到 2.0
    标准化建议: np.log(ratio)

    Usage:
        >>> # 基本用法
        >>> returns = df['close'].pct_change()
        >>> vol_regime = calculate_volatility_regime(returns)
        >>>
        >>> # 识别波动率突变
        >>> vol_spike = vol_regime > 1.5  # 波动率激增
        >>> vol_calm = vol_regime < 0.7   # 波动率平静
        >>>
        >>> # 动态仓位调整
        >>> base_position = 0.1
        >>> vol_adjusted_position = base_position / vol_regime  # 高波动率降仓位
        >>>
        >>> # 策略选择
        >>> trend_strategy_signal = vol_regime > 1.2  # 高波动率适合趋势策略
        >>> mean_revert_signal = vol_regime < 0.8     # 低波动率适合均值回归
        >>>
        >>> # 风险预警系统
        >>> risk_level = pd.cut(vol_regime,
        ...                    bins=[0, 0.7, 1.0, 1.5, np.inf],
        ...                    labels=['低风险', '正常', '高风险', '极高风险'])

    Note:
        - 波动率计算基于历史数据，可能滞后于市场变化
        - 在市场结构性变化时，该指标可能失效
        - 建议与其他市场情绪指标结合使用
        - 可以考虑使用GARCH模型进行更精确的波动率预测
    """
    short_vol = returns.rolling(short_window).std()
    long_vol = returns.rolling(long_window).std()
    vol_regime = short_vol / (long_vol + 1e-8)
    return vol_regime


def calculate_mean_reversion_signal(close: pd.Series, window: int = 20, threshold: float = 2.0) -> pd.Series:
    """
    计算均值回归信号 - 识别价格偏离均值的程度和方向

    均值回归是金融市场的重要特征，该指标通过测量价格相对于
    移动平均线的标准化偏离程度，为均值回归策略提供交易信号。

    Args:
        close (pd.Series): 收盘价序列
        window (int): 移动平均窗口，默认20日
                     - 短期：10-20日，适合短线均值回归
                     - 中期：20-60日，适合中线策略
                     - 长期：60-252日，适合长线价值回归
        threshold (float): 标准差倍数阈值，默认2.0
                          - 较小值(1.0-1.5)：更敏感，信号更频繁
                          - 较大值(2.0-3.0)：更保守，信号更可靠

    Returns:
        pd.Series: 均值回归信号强度，范围 -1 到 1
                  - 信号 > 0.5: 强烈超买，考虑卖出
                  - 0.2 < 信号 < 0.5: 轻度超买
                  - -0.2 < 信号 < 0.2: 正常区间
                  - -0.5 < 信号 < -0.2: 轻度超卖
                  - 信号 < -0.5: 强烈超卖，考虑买入

    范围: -1 到 1，天然标准化

    Usage:
        >>> # 基本用法
        >>> mean_revert_signal = calculate_mean_reversion_signal(df['close'])
        >>>
        >>> # 交易信号生成
        >>> buy_signal = mean_revert_signal < -0.5   # 强烈超卖买入
        >>> sell_signal = mean_revert_signal > 0.5   # 强烈超买卖出
        >>>
        >>> # 多周期确认
        >>> short_signal = calculate_mean_reversion_signal(df['close'], window=10)
        >>> long_signal = calculate_mean_reversion_signal(df['close'], window=60)
        >>> confirmed_buy = (short_signal < -0.5) & (long_signal < -0.3)
        >>>
        >>> # 动态阈值调整
        >>> volatility = df['close'].pct_change().rolling(20).std()
        >>> dynamic_threshold = 2.0 * (1 + volatility)  # 高波动率提高阈值
        >>> adaptive_signal = calculate_mean_reversion_signal(
        ...     df['close'], threshold=dynamic_threshold.iloc[-1]
        ... )
        >>>
        >>> # 仓位大小调整
        >>> position_size = abs(mean_revert_signal) * 0.1  # 信号强度决定仓位
        >>> position_direction = np.sign(-mean_revert_signal)  # 反向操作

    Note:
        - 均值回归策略在趋势市场中可能失效
        - 建议结合趋势指标避免逆势操作
        - 在使用前应验证标的资产的均值回归特性
        - 可以考虑使用布林带等其他均值回归指标进行确认
    """
    ma = close.rolling(window).mean()
    std = close.rolling(window).std()
    z_score = (close - ma) / (std + 1e-8)

    # 将z-score转换为信号强度
    signal = np.tanh(z_score / threshold)
    return signal


def calculate_trend_strength(close: pd.Series, window: int = 20) -> pd.Series:
    """
    计算趋势强度指标 - 量化价格趋势的持续性和可靠性

    趋势强度指标使用线性回归的决定系数(R²)来衡量价格变动的
    线性程度，高R²值表示价格沿着明确的趋势方向运动，
    是趋势跟踪策略的重要参考指标。

    Args:
        close (pd.Series): 收盘价序列
        window (int): 计算窗口，默认20日
                     - 短期：10-20日，捕捉短期趋势
                     - 中期：20-60日，识别中期趋势
                     - 长期：60-120日，确认长期趋势

    Returns:
        pd.Series: 趋势强度，范围 0 到 1
                  - 强度 > 0.8: 非常强的趋势，高置信度
                  - 0.6 < 强度 < 0.8: 较强趋势
                  - 0.4 < 强度 < 0.6: 中等趋势
                  - 0.2 < 强度 < 0.4: 弱趋势
                  - 强度 < 0.2: 无明显趋势，震荡状态

    范围: 0 到 1，天然标准化

    Usage:
        >>> # 基本用法
        >>> trend_strength = calculate_trend_strength(df['close'])
        >>>
        >>> # 趋势确认
        >>> strong_trend = trend_strength > 0.7
        >>> weak_trend = trend_strength < 0.3
        >>>
        >>> # 策略选择
        >>> # 强趋势时使用趋势跟踪策略
        >>> momentum_strategy = trend_strength > 0.6
        >>> # 弱趋势时使用均值回归策略
        >>> mean_revert_strategy = trend_strength < 0.4
        >>>
        >>> # 多周期趋势分析
        >>> short_trend = calculate_trend_strength(df['close'], window=10)
        >>> long_trend = calculate_trend_strength(df['close'], window=60)
        >>> trend_acceleration = short_trend > long_trend  # 趋势加速
        >>>
        >>> # 结合价格方向
        >>> price_direction = df['close'].pct_change(window).apply(np.sign)
        >>> uptrend_strength = trend_strength * (price_direction > 0)
        >>> downtrend_strength = trend_strength * (price_direction < 0)

    Note:
        - R²值对窗口大小敏感，需要根据分析目标选择合适窗口
        - 在价格剧烈波动时，该指标可能给出误导性信号
        - 建议与其他趋势指标（如移动平均线）结合使用
        - 对于非线性趋势，该指标的效果可能有限
    """
    # 使用线性回归斜率的R²作为趋势强度
    def trend_strength_calc(prices):
        if len(prices) < 3:
            return np.nan
        x = np.arange(len(prices))
        y = prices.values

        # 计算线性回归
        x_mean = x.mean()
        y_mean = y.mean()

        numerator = np.sum((x - x_mean) * (y - y_mean))
        denominator_x = np.sum((x - x_mean) ** 2)
        denominator_y = np.sum((y - y_mean) ** 2)

        if denominator_x == 0 or denominator_y == 0:
            return 0

        correlation = numerator / np.sqrt(denominator_x * denominator_y)
        r_squared = correlation ** 2

        return r_squared

    trend_strength = close.rolling(window).apply(trend_strength_calc, raw=False)
    return trend_strength


def calculate_support_resistance_levels(close: pd.Series, window: int = 60) -> Tuple[pd.Series, pd.Series]:
    """
    计算支撑阻力位距离 - 识别关键价格水平的相对位置

    支撑阻力位是技术分析的核心概念，通过计算当前价格到
    历史高低点的相对距离，可以识别潜在的买卖点和风险水平。

    Args:
        close (pd.Series): 收盘价序列
        window (int): 计算窗口，默认60日
                     - 短期：20-40日，识别短期支撑阻力
                     - 中期：60-120日，识别中期关键位
                     - 长期：252日以上，识别长期重要位

    Returns:
        Tuple[pd.Series, pd.Series]: (支撑位距离, 阻力位距离)
                                   - 支撑位距离: 当前价格高于支撑位的百分比
                                   - 阻力位距离: 当前价格低于阻力位的百分比
                                   - 距离越小，越接近关键位

    Usage:
        >>> # 基本用法
        >>> support_dist, resistance_dist = calculate_support_resistance_levels(df['close'])
        >>>
        >>> # 识别接近支撑位的情况
        >>> near_support = support_dist < 0.02  # 距离支撑位不到2%
        >>> near_resistance = resistance_dist < 0.02  # 距离阻力位不到2%
        >>>
        >>> # 突破信号
        >>> support_break = (support_dist.shift(1) > 0) & (support_dist <= 0)
        >>> resistance_break = (resistance_dist.shift(1) > 0) & (resistance_dist <= 0)
        >>>
        >>> # 风险评估
        >>> risk_score = 1 - support_dist  # 越接近支撑位风险越高
        >>> opportunity_score = 1 - resistance_dist  # 越接近阻力位机会越大
        >>>
        >>> # 多周期确认
        >>> short_support, short_resistance = calculate_support_resistance_levels(
        ...     df['close'], window=20
        ... )
        >>> long_support, long_resistance = calculate_support_resistance_levels(
        ...     df['close'], window=120
        ... )
        >>> # 多周期支撑重合区域
        >>> strong_support = (short_support < 0.03) & (long_support < 0.05)

    Note:
        - 支撑阻力位的有效性取决于历史价格的代表性
        - 在趋势市场中，支撑阻力位可能被频繁突破
        - 建议结合成交量确认支撑阻力位的有效性
        - 可以考虑使用分形或枢轴点等更精确的方法
    """
    rolling_min = close.rolling(window).min()
    rolling_max = close.rolling(window).max()

    # 计算相对距离
    support_distance = (close - rolling_min) / close
    resistance_distance = (rolling_max - close) / close

    return support_distance, resistance_distance


def calculate_liquidity_score(volume: pd.Series, close: pd.Series, window: int = 20) -> pd.Series:
    """
    计算流动性评分 - 衡量股票交易的便利性和成本

    流动性是投资决策的重要考虑因素，高流动性意味着更容易
    以合理价格买卖股票。该指标通过成交额的稳定性来评估流动性，
    成交额越稳定，流动性越好。

    Args:
        volume (pd.Series): 成交量序列
        close (pd.Series): 收盘价序列
        window (int): 计算窗口，默认20日
                     - 建议使用20-60日窗口平衡稳定性和敏感性

    Returns:
        pd.Series: 流动性评分，范围 0 到 1
                  - 评分 > 0.8: 高流动性，适合大额交易
                  - 0.6 < 评分 < 0.8: 良好流动性
                  - 0.4 < 评分 < 0.6: 中等流动性
                  - 0.2 < 评分 < 0.4: 较差流动性
                  - 评分 < 0.2: 低流动性，交易需谨慎

    范围: 0 到 1，天然标准化

    Usage:
        >>> # 基本用法
        >>> liquidity = calculate_liquidity_score(df['volume'], df['close'])
        >>>
        >>> # 流动性筛选
        >>> high_liquidity_stocks = liquidity > 0.7
        >>>
        >>> # 交易成本估算
        >>> # 低流动性股票可能有更高的买卖价差
        >>> estimated_spread = (1 - liquidity) * 0.01  # 简化的价差估算
        >>>
        >>> # 仓位限制
        >>> max_position = liquidity * 0.1  # 流动性越好，允许仓位越大
        >>>
        >>> # 流动性风险调整
        >>> liquidity_adjusted_return = df['close'].pct_change() * liquidity
        >>>
        >>> # 多周期流动性分析
        >>> short_liquidity = calculate_liquidity_score(df['volume'], df['close'], 10)
        >>> long_liquidity = calculate_liquidity_score(df['volume'], df['close'], 60)
        >>> liquidity_trend = short_liquidity - long_liquidity  # 流动性变化趋势

    Note:
        - 该指标基于成交额的变异系数，适用于大多数市场
        - 在市场极端情况下，流动性可能急剧恶化
        - 建议结合市场深度等其他流动性指标
        - 对于小盘股，该指标可能不够敏感
    """
    # 使用成交额的变异系数的倒数作为流动性指标
    amount = volume * close
    amount_mean = amount.rolling(window).mean()
    amount_std = amount.rolling(window).std()

    cv = amount_std / (amount_mean + 1e-8)  # 变异系数
    liquidity_score = 1 / (1 + cv)  # 转换为0-1分数

    return liquidity_score


def calculate_market_cap_factor(close: pd.Series, shares_outstanding: Union[float, pd.Series]) -> pd.Series:
    """
    计算市值因子 - 量化公司规模对投资回报的影响

    市值是最重要的风险因子之一，历史上小盘股和大盘股
    表现出不同的风险收益特征。对数变换可以减少极值影响，
    使得市值因子更适合用于量化模型。

    Args:
        close (pd.Series): 收盘价序列
        shares_outstanding (Union[float, pd.Series]): 流通股本
                                                    - 可以是固定值或时间序列
                                                    - 建议使用最新的流通股本

    Returns:
        pd.Series: 对数市值
                  - 值越大，公司规模越大
                  - 通常用于风险模型和选股模型

    标准化建议: 已使用对数变换，可直接使用

    Usage:
        >>> # 基本用法
        >>> log_market_cap = calculate_market_cap_factor(df['close'], 1e8)
        >>>
        >>> # 市值分类
        >>> market_cap = np.exp(log_market_cap)  # 还原为实际市值
        >>> large_cap = market_cap > 1e11    # 大盘股（>1000亿）
        >>> mid_cap = (market_cap > 1e10) & (market_cap <= 1e11)  # 中盘股
        >>> small_cap = market_cap <= 1e10   # 小盘股
        >>>
        >>> # 市值中性化
        >>> returns = df['close'].pct_change()
        >>> # 在横截面上对市值因子进行回归，获得市值中性收益
        >>>
        >>> # 动态股本处理
        >>> # 如果股本发生变化（如增发、回购）
        >>> dynamic_shares = pd.Series([1e8, 1.1e8, 1.05e8], index=df.index[:3])
        >>> log_cap = calculate_market_cap_factor(df['close'], dynamic_shares)
        >>>
        >>> # 市值加权组合
        >>> weights = market_cap / market_cap.sum()  # 市值权重
        >>>
        >>> # 风险模型中的应用
        >>> # 通常作为第一个风险因子，解释股票收益的系统性差异

    Note:
        - 市值计算需要准确的股本数据
        - 在股本变动（如股票分割、增发）时需要及时更新
        - 对数变换有助于处理市值的偏态分布
        - 在多因子模型中，市值因子通常是最重要的解释变量
    """
    if isinstance(shares_outstanding, (int, float)):
        shares_outstanding = pd.Series(shares_outstanding, index=close.index)

    market_cap = close * shares_outstanding
    log_market_cap = np.log(market_cap + 1)

    return log_market_cap


# ==================== 使用示例和最佳实践 ====================

def create_comprehensive_feature_set(df: pd.DataFrame,
                                    market_df: pd.DataFrame = None,
                                    shares_outstanding: float = 1e8) -> pd.DataFrame:
    """
    创建综合特征集 - 一站式特征工程解决方案

    该函数整合了本模块的所有特征计算函数，为量化策略开发
    提供一个完整的特征集。所有特征都经过标准化处理，
    可以直接用于机器学习模型。

    Args:
        df (pd.DataFrame): 股票数据，必须包含 ['close', 'volume'] 列
        market_df (pd.DataFrame, optional): 市场指数数据，包含 ['close'] 列
        shares_outstanding (float): 流通股本，默认1亿股

    Returns:
        pd.DataFrame: 包含所有特征的DataFrame

    Usage:
        >>> # 准备数据
        >>> stock_data = pd.DataFrame({
        ...     'close': [100, 101, 99, 102, 98],
        ...     'volume': [1000000, 1200000, 800000, 1500000, 900000]
        ... })
        >>> market_data = pd.DataFrame({
        ...     'close': [3000, 3010, 2990, 3020, 2980]
        ... })
        >>>
        >>> # 生成特征集
        >>> features = create_comprehensive_feature_set(
        ...     stock_data, market_data, shares_outstanding=1e8
        ... )
        >>>
        >>> # 查看特征
        >>> print(features.columns.tolist())
        >>> print(features.describe())
    """
    features = pd.DataFrame(index=df.index)

    # 基础数据准备
    close = df['close']
    volume = df['volume']
    returns = close.pct_change()

    # 1. 排名特征（天然标准化）
    features['price_rank_60d'] = calculate_price_rank(close, window=60)
    features['price_rank_252d'] = calculate_price_rank(close, window=252)
    features['volume_rank_60d'] = calculate_volume_rank(volume, window=60)
    features['volume_rank_252d'] = calculate_volume_rank(volume, window=252)

    # 2. 风险调整收益指标
    features['return_skewness'] = calculate_return_skewness(returns, window=60)
    features['return_kurtosis'] = calculate_return_kurtosis(returns, window=60)
    features['sharpe_ratio'] = calculate_sharpe_ratio(returns, window=60)
    features['max_drawdown'] = calculate_max_drawdown_rolling(returns, window=60)

    # 3. 市场相关性指标（如果提供市场数据）
    if market_df is not None:
        market_returns = market_df['close'].pct_change()
        features['beta'] = calculate_beta(returns, market_returns, window=60)
        features['information_ratio'] = calculate_information_ratio(returns, market_returns, window=60)

    # 4. 流动性和交易活跃度
    features['turnover_rate'] = calculate_turnover_rate(volume, shares_outstanding, window=20)
    features['liquidity_score'] = calculate_liquidity_score(volume, close, window=20)

    # 5. 多周期动量特征
    momentum_df = calculate_price_momentum_multiple_periods(close, periods=[5, 10, 20, 60])
    for col in momentum_df.columns:
        # 标准化动量特征
        rolling_std = momentum_df[col].rolling(252).std()
        features[f'{col}_normalized'] = np.tanh(momentum_df[col] / (rolling_std + 1e-8))

    # 6. 市场微观结构特征
    features['volatility_regime'] = calculate_volatility_regime(returns, short_window=20, long_window=60)
    features['mean_reversion_signal'] = calculate_mean_reversion_signal(close, window=20, threshold=2.0)
    features['trend_strength'] = calculate_trend_strength(close, window=20)

    # 7. 支撑阻力位特征
    support_dist, resistance_dist = calculate_support_resistance_levels(close, window=60)
    features['support_distance'] = support_dist
    features['resistance_distance'] = resistance_dist

    # 8. 市值因子
    features['log_market_cap'] = calculate_market_cap_factor(close, shares_outstanding)

    # 9. 额外的标准化处理
    # 对非天然标准化的特征进行处理
    features['return_skewness_norm'] = np.tanh(features['return_skewness'] / 2)
    features['return_kurtosis_norm'] = np.tanh((features['return_kurtosis'] - 3) / 3)
    features['sharpe_ratio_norm'] = np.tanh(features['sharpe_ratio'] / 2)
    features['max_drawdown_positive'] = -features['max_drawdown']  # 转为正值
    features['volatility_regime_log'] = np.log(features['volatility_regime'] + 1e-8)
    features['turnover_rate_log'] = np.log(1 + features['turnover_rate'])

    if market_df is not None:
        features['beta_norm'] = np.tanh((features['beta'] - 1) / 1)
        features['information_ratio_norm'] = np.tanh(features['information_ratio'] / 1)

    return features


# ==================== 最佳实践指南 ====================

"""
量化特征工程最佳实践指南

1. 数据质量检查
   - 在计算特征前，务必检查数据的完整性和准确性
   - 处理缺失值、异常值和数据跳跃
   - 确保价格数据已经过复权处理

2. 特征选择原则
   - 根据策略类型选择相关特征：
     * 趋势策略：动量、趋势强度、Beta
     * 均值回归策略：价格排名、均值回归信号、支撑阻力位
     * 风险管理：波动率状态、最大回撤、偏度峰度

3. 标准化处理
   - 所有特征在使用前都应进行标准化
   - 推荐使用本模块提供的标准化建议
   - 对于机器学习模型，考虑使用Z-score标准化

4. 时间窗口选择
   - 短期特征（5-20日）：适合高频交易和短线策略
   - 中期特征（20-60日）：适合中线策略和风险管理
   - 长期特征（60-252日）：适合长线投资和基本面分析

5. 特征组合策略
   - 多周期确认：使用不同时间窗口的同类特征进行确认
   - 多维度验证：结合价格、成交量、波动率等不同维度
   - 因子正交化：在多因子模型中注意因子间的相关性

6. 回测验证
   - 所有特征都应通过历史回测验证有效性
   - 注意前瞻偏差和数据泄露问题
   - 考虑交易成本和市场冲击的影响

7. 实时更新
   - 建立特征的实时计算和更新机制
   - 监控特征分布的变化，及时调整参数
   - 定期重新训练和验证模型

示例工作流程：
```python
# 1. 数据准备
stock_data = load_stock_data('000001.SZ')
market_data = load_market_data('000300.SH')

# 2. 数据质量检查
stock_data = clean_and_validate_data(stock_data)

# 3. 特征计算
features = create_comprehensive_feature_set(
    stock_data, market_data, shares_outstanding=1e9
)

# 4. 特征筛选
selected_features = select_features_by_strategy(features, strategy_type='momentum')

# 5. 模型训练
model = train_quantitative_model(selected_features, target_returns)

# 6. 回测验证
backtest_results = run_backtest(model, selected_features, test_period)
```
"""
