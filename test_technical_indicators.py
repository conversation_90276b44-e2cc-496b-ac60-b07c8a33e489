"""
技术指标模块测试文件
测试各个技术指标函数的正确性和边界条件
"""
import unittest
import pandas as pd
import numpy as np
from technical_indicators import *
from quantitative_features import *


class TestTechnicalIndicators(unittest.TestCase):
    
    def setUp(self):
        """设置测试数据"""
        # 创建模拟的OHLCV数据
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', periods=100, freq='D')
        
        # 生成模拟价格数据（随机游走）
        returns = np.random.normal(0, 0.02, 100)
        prices = 100 * np.exp(np.cumsum(returns))
        
        self.df = pd.DataFrame({
            'close': prices,
            'open': prices * (1 + np.random.normal(0, 0.005, 100)),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.01, 100))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.01, 100))),
            'volume': np.random.randint(1000000, 10000000, 100),
            'returns': returns
        }, index=dates)
        
        # 确保high >= close >= low
        self.df['high'] = np.maximum(self.df['high'], self.df['close'])
        self.df['low'] = np.minimum(self.df['low'], self.df['close'])
    
    def test_rsi_calculation(self):
        """测试RSI计算"""
        rsi = calculate_rsi(self.df['close'])
        
        # 检查RSI范围
        valid_rsi = rsi.dropna()
        self.assertTrue((valid_rsi >= 0).all(), "RSI应该大于等于0")
        self.assertTrue((valid_rsi <= 100).all(), "RSI应该小于等于100")
        
        # 检查返回值类型
        self.assertIsInstance(rsi, pd.Series)
        
        # 检查长度
        self.assertEqual(len(rsi), len(self.df))
    
    def test_bollinger_bands(self):
        """测试布林带计算"""
        upper, middle, lower = calculate_bollinger_bands(self.df['close'])
        
        # 检查布林带关系
        valid_data = pd.concat([upper, middle, lower], axis=1).dropna()
        self.assertTrue((valid_data.iloc[:, 0] >= valid_data.iloc[:, 1]).all(), "上轨应该大于等于中轨")
        self.assertTrue((valid_data.iloc[:, 1] >= valid_data.iloc[:, 2]).all(), "中轨应该大于等于下轨")
    
    def test_bb_position(self):
        """测试布林带位置计算"""
        bb_pos = calculate_bb_position(self.df['close'])
        
        # 检查范围
        valid_pos = bb_pos.dropna()
        self.assertTrue((valid_pos >= 0).all(), "布林带位置应该大于等于0")
        self.assertTrue((valid_pos <= 1).all(), "布林带位置应该小于等于1")
    
    def test_moving_average_ratios(self):
        """测试移动平均比率"""
        ma_ratios = calculate_moving_average_ratios(self.df['close'])
        
        # 检查列名
        expected_cols = ['ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20', 'ma_ratio_30']
        self.assertEqual(list(ma_ratios.columns), expected_cols)
        
        # 检查比率为正数
        for col in ma_ratios.columns:
            valid_ratios = ma_ratios[col].dropna()
            self.assertTrue((valid_ratios > 0).all(), f"{col}应该为正数")
    
    def test_atr_calculation(self):
        """测试ATR计算"""
        atr = calculate_atr(self.df['high'], self.df['low'], self.df['close'])
        
        # 检查ATR为正数
        valid_atr = atr.dropna()
        self.assertTrue((valid_atr >= 0).all(), "ATR应该大于等于0")
        
        # 检查返回值类型
        self.assertIsInstance(atr, pd.Series)
    
    def test_atr_ratio(self):
        """测试ATR比率"""
        atr_ratio = calculate_atr_ratio(self.df['high'], self.df['low'], self.df['close'])
        
        # 检查比率为正数
        valid_ratio = atr_ratio.dropna()
        self.assertTrue((valid_ratio >= 0).all(), "ATR比率应该大于等于0")
    
    def test_volume_ratio(self):
        """测试成交量比率"""
        vol_ratio = calculate_volume_ratio(self.df['volume'])
        
        # 检查比率为正数
        valid_ratio = vol_ratio.dropna()
        self.assertTrue((valid_ratio > 0).all(), "成交量比率应该为正数")
    
    def test_volatility(self):
        """测试波动率计算"""
        volatility = calculate_volatility(self.df['returns'])
        
        # 检查波动率为正数
        valid_vol = volatility.dropna()
        self.assertTrue((valid_vol >= 0).all(), "波动率应该大于等于0")
    
    def test_macd(self):
        """测试MACD计算"""
        macd_line, signal_line, histogram = calculate_macd(self.df['close'])
        
        # 检查返回值类型
        self.assertIsInstance(macd_line, pd.Series)
        self.assertIsInstance(signal_line, pd.Series)
        self.assertIsInstance(histogram, pd.Series)
        
        # 检查长度一致
        self.assertEqual(len(macd_line), len(self.df))
        self.assertEqual(len(signal_line), len(self.df))
        self.assertEqual(len(histogram), len(self.df))
    
    def test_williams_r(self):
        """测试威廉指标"""
        willr = calculate_williams_r(self.df['high'], self.df['low'], self.df['close'])
        
        # 检查范围
        valid_willr = willr.dropna()
        self.assertTrue((valid_willr >= -100).all(), "Williams %R应该大于等于-100")
        self.assertTrue((valid_willr <= 0).all(), "Williams %R应该小于等于0")
    
    def test_momentum(self):
        """测试动量指标"""
        momentum = calculate_momentum(self.df['close'])
        
        # 检查返回值类型
        self.assertIsInstance(momentum, pd.Series)
        
        # 检查长度
        self.assertEqual(len(momentum), len(self.df))
    
    def test_ema_ratios(self):
        """测试EMA比率"""
        ema_ratios = calculate_ema_ratios(self.df['close'])
        
        # 检查列名
        expected_cols = ['ema_ratio_5', 'ema_ratio_10', 'ema_ratio_20', 'ema_ratio_30']
        self.assertEqual(list(ema_ratios.columns), expected_cols)
        
        # 检查比率为正数
        for col in ema_ratios.columns:
            valid_ratios = ema_ratios[col].dropna()
            self.assertTrue((valid_ratios > 0).all(), f"{col}应该为正数")
    
    def test_adx(self):
        """测试ADX计算"""
        adx = calculate_adx(self.df['high'], self.df['low'], self.df['close'])
        
        # 检查ADX范围
        valid_adx = adx.dropna()
        self.assertTrue((valid_adx >= 0).all(), "ADX应该大于等于0")
        self.assertTrue((valid_adx <= 100).all(), "ADX应该小于等于100")
    
    def test_edge_cases(self):
        """测试边界条件"""
        # 测试空序列
        empty_series = pd.Series([], dtype=float)
        rsi_empty = calculate_rsi(empty_series)
        self.assertTrue(rsi_empty.empty)
        
        # 测试包含NaN的序列
        nan_series = pd.Series([100, np.nan, 102, 103, np.nan])
        rsi_nan = calculate_rsi(nan_series)
        self.assertIsInstance(rsi_nan, pd.Series)
        
        # 测试常数序列
        constant_series = pd.Series([100] * 50)
        rsi_constant = calculate_rsi(constant_series)
        # RSI在价格不变时会产生NaN（因为没有涨跌），这是正常的
        # 我们只检查函数不会崩溃
        self.assertIsInstance(rsi_constant, pd.Series)


class TestQuantitativeFeatures(unittest.TestCase):
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', periods=300, freq='D')  # 更长的序列用于测试
        
        returns = np.random.normal(0, 0.02, 300)
        prices = 100 * np.exp(np.cumsum(returns))
        
        self.df = pd.DataFrame({
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 300),
            'returns': returns
        }, index=dates)
        
        # 模拟市场收益率
        self.market_returns = pd.Series(np.random.normal(0, 0.015, 300), index=dates)
    
    def test_price_rank(self):
        """测试价格排名"""
        price_rank = calculate_price_rank(self.df['close'])
        
        # 检查范围
        valid_rank = price_rank.dropna()
        self.assertTrue((valid_rank >= 0).all(), "价格排名应该大于等于0")
        self.assertTrue((valid_rank <= 1).all(), "价格排名应该小于等于1")
    
    def test_volume_rank(self):
        """测试成交量排名"""
        volume_rank = calculate_volume_rank(self.df['volume'])
        
        # 检查范围
        valid_rank = volume_rank.dropna()
        self.assertTrue((valid_rank >= 0).all(), "成交量排名应该大于等于0")
        self.assertTrue((valid_rank <= 1).all(), "成交量排名应该小于等于1")
    
    def test_return_skewness(self):
        """测试收益率偏度"""
        skewness = calculate_return_skewness(self.df['returns'])
        
        # 检查返回值类型
        self.assertIsInstance(skewness, pd.Series)
        
        # 偏度通常在合理范围内
        valid_skew = skewness.dropna()
        if len(valid_skew) > 0:
            self.assertTrue((valid_skew >= -10).all(), "偏度不应该过于极端")
            self.assertTrue((valid_skew <= 10).all(), "偏度不应该过于极端")
    
    def test_sharpe_ratio(self):
        """测试夏普比率"""
        sharpe = calculate_sharpe_ratio(self.df['returns'])
        
        # 检查返回值类型
        self.assertIsInstance(sharpe, pd.Series)
        
        # 夏普比率通常在合理范围内
        valid_sharpe = sharpe.dropna()
        if len(valid_sharpe) > 0:
            self.assertTrue((valid_sharpe >= -5).all(), "夏普比率不应该过于极端")
            self.assertTrue((valid_sharpe <= 5).all(), "夏普比率不应该过于极端")
    
    def test_beta_calculation(self):
        """测试Beta计算"""
        beta = calculate_beta(self.df['returns'], self.market_returns)
        
        # 检查返回值类型
        self.assertIsInstance(beta, pd.Series)
        
        # Beta通常在合理范围内
        valid_beta = beta.dropna()
        if len(valid_beta) > 0:
            self.assertTrue((valid_beta >= -2).all(), "Beta不应该过于极端")
            self.assertTrue((valid_beta <= 5).all(), "Beta不应该过于极端")
    
    def test_momentum_multiple_periods(self):
        """测试多周期动量"""
        momentum_df = calculate_price_momentum_multiple_periods(self.df['close'])
        
        # 检查列名
        expected_cols = ['momentum_5d', 'momentum_10d', 'momentum_20d', 'momentum_60d']
        self.assertEqual(list(momentum_df.columns), expected_cols)
        
        # 检查数据类型
        self.assertIsInstance(momentum_df, pd.DataFrame)
    
    def test_trend_strength(self):
        """测试趋势强度"""
        trend_strength = calculate_trend_strength(self.df['close'])
        
        # 检查范围
        valid_strength = trend_strength.dropna()
        self.assertTrue((valid_strength >= 0).all(), "趋势强度应该大于等于0")
        self.assertTrue((valid_strength <= 1).all(), "趋势强度应该小于等于1")


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
